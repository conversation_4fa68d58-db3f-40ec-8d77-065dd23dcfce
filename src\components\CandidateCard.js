import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const CandidateCard = ({ 
  candidate, 
  category,
  isSelected, 
  onPress, 
  disabled = false,
  showDetails = false 
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.candidateCard, 
        isSelected && styles.candidateCardSelected,
        disabled && styles.candidateCardDisabled
      ]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <View style={styles.candidateHeader}>
        <View style={[styles.candidateAvatar, { backgroundColor: category.color }]}>
          <Text style={styles.candidateInitial}>
            {candidate.name.charAt(0).toUpperCase()}
          </Text>
        </View>
        <View style={styles.candidateInfo}>
          <Text style={styles.candidateName}>{candidate.name}</Text>
          <Text style={styles.candidateDescription}>{candidate.description}</Text>
          {showDetails && candidate.bio && (
            <Text style={styles.candidateBio}>{candidate.bio}</Text>
          )}
        </View>
        <View style={styles.selectionIndicator}>
          {isSelected ? (
            <Ionicons name="radio-button-on" size={24} color={category.color} />
          ) : (
            <Ionicons name="radio-button-off" size={24} color="#b0b0b0" />
          )}
        </View>
      </View>

      {showDetails && candidate.achievements && candidate.achievements.length > 0 && (
        <View style={styles.achievementsSection}>
          <Text style={styles.achievementsTitle}>Réalisations :</Text>
          {candidate.achievements.map((achievement, index) => (
            <View key={index} style={styles.achievementItem}>
              <Ionicons name="star" size={12} color={category.color} />
              <Text style={styles.achievementText}>{achievement}</Text>
            </View>
          ))}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  candidateCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  candidateCardSelected: {
    borderColor: '#4CAF50',
    backgroundColor: '#1e2a1e',
  },
  candidateCardDisabled: {
    opacity: 0.6,
  },
  candidateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  candidateAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  candidateInitial: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  candidateInfo: {
    flex: 1,
    marginLeft: 15,
  },
  candidateName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  candidateDescription: {
    color: '#b0b0b0',
    fontSize: 14,
    marginTop: 2,
  },
  candidateBio: {
    color: '#888',
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  selectionIndicator: {
    marginLeft: 10,
  },
  achievementsSection: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#2a2a3e',
  },
  achievementsTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  achievementText: {
    color: '#b0b0b0',
    fontSize: 11,
    marginLeft: 6,
    flex: 1,
  },
});

export default CandidateCard;
