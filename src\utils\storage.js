import AsyncStorage from '@react-native-async-storage/async-storage';

// Clés de stockage
const STORAGE_KEYS = {
  USER_DATA: 'userData',
  USER_VOTES: 'userVotes',
  VOTES_SUBMITTED: 'votesSubmitted',
  SUBMISSION_DATE: 'submissionDate',
  APP_SETTINGS: 'appSettings',
};

// Gestion des données utilisateur
export const UserStorage = {
  // Sauvegarder les données utilisateur
  async saveUserData(userData) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données utilisateur:', error);
      return false;
    }
  },

  // Charger les données utilisateur
  async getUserData() {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Erreur lors du chargement des données utilisateur:', error);
      return null;
    }
  },

  // Supprimer les données utilisateur
  async clearUserData() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression des données utilisateur:', error);
      return false;
    }
  },

  // Vérifier si l'utilisateur est connecté
  async isUserAuthenticated() {
    try {
      const userData = await this.getUserData();
      return userData && userData.isAuthenticated === true;
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'authentification:', error);
      return false;
    }
  },
};

// Gestion des votes
export const VoteStorage = {
  // Sauvegarder un vote pour une catégorie
  async saveVote(categoryId, candidateId) {
    try {
      const existingVotes = await this.getAllVotes();
      const updatedVotes = {
        ...existingVotes,
        [categoryId]: candidateId,
      };
      await AsyncStorage.setItem(STORAGE_KEYS.USER_VOTES, JSON.stringify(updatedVotes));
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du vote:', error);
      return false;
    }
  },

  // Charger tous les votes
  async getAllVotes() {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.USER_VOTES);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Erreur lors du chargement des votes:', error);
      return {};
    }
  },

  // Charger un vote spécifique
  async getVote(categoryId) {
    try {
      const votes = await this.getAllVotes();
      return votes[categoryId] || null;
    } catch (error) {
      console.error('Erreur lors du chargement du vote:', error);
      return null;
    }
  },

  // Supprimer un vote
  async removeVote(categoryId) {
    try {
      const existingVotes = await this.getAllVotes();
      delete existingVotes[categoryId];
      await AsyncStorage.setItem(STORAGE_KEYS.USER_VOTES, JSON.stringify(existingVotes));
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression du vote:', error);
      return false;
    }
  },

  // Supprimer tous les votes
  async clearAllVotes() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_VOTES);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression des votes:', error);
      return false;
    }
  },

  // Marquer les votes comme soumis
  async markVotesAsSubmitted() {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.VOTES_SUBMITTED, 'true');
      await AsyncStorage.setItem(STORAGE_KEYS.SUBMISSION_DATE, new Date().toISOString());
      return true;
    } catch (error) {
      console.error('Erreur lors de la soumission des votes:', error);
      return false;
    }
  },

  // Vérifier si les votes ont été soumis
  async areVotesSubmitted() {
    try {
      const submitted = await AsyncStorage.getItem(STORAGE_KEYS.VOTES_SUBMITTED);
      return submitted === 'true';
    } catch (error) {
      console.error('Erreur lors de la vérification de la soumission:', error);
      return false;
    }
  },

  // Obtenir la date de soumission
  async getSubmissionDate() {
    try {
      const dateStr = await AsyncStorage.getItem(STORAGE_KEYS.SUBMISSION_DATE);
      return dateStr ? new Date(dateStr) : null;
    } catch (error) {
      console.error('Erreur lors du chargement de la date de soumission:', error);
      return null;
    }
  },
};

// Gestion des paramètres de l'application
export const AppStorage = {
  // Sauvegarder les paramètres
  async saveSettings(settings) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
      return false;
    }
  },

  // Charger les paramètres
  async getSettings() {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.APP_SETTINGS);
      return data ? JSON.parse(data) : {
        notifications: true,
        theme: 'dark',
        language: 'fr',
      };
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error);
      return {
        notifications: true,
        theme: 'dark',
        language: 'fr',
      };
    }
  },

  // Mettre à jour un paramètre spécifique
  async updateSetting(key, value) {
    try {
      const settings = await this.getSettings();
      settings[key] = value;
      await this.saveSettings(settings);
      return true;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du paramètre:', error);
      return false;
    }
  },
};

// Utilitaires généraux
export const StorageUtils = {
  // Vider tout le stockage (pour la déconnexion)
  async clearAllData() {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.USER_VOTES,
        STORAGE_KEYS.VOTES_SUBMITTED,
        STORAGE_KEYS.SUBMISSION_DATE,
      ]);
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression de toutes les données:', error);
      return false;
    }
  },

  // Obtenir la taille du stockage utilisé
  async getStorageSize() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const stores = await AsyncStorage.multiGet(keys);
      let totalSize = 0;
      
      stores.forEach(([key, value]) => {
        if (value) {
          totalSize += value.length;
        }
      });
      
      return totalSize;
    } catch (error) {
      console.error('Erreur lors du calcul de la taille du stockage:', error);
      return 0;
    }
  },

  // Exporter toutes les données (pour le debug)
  async exportAllData() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const stores = await AsyncStorage.multiGet(keys);
      const data = {};
      
      stores.forEach(([key, value]) => {
        if (value) {
          try {
            data[key] = JSON.parse(value);
          } catch {
            data[key] = value;
          }
        }
      });
      
      return data;
    } catch (error) {
      console.error('Erreur lors de l\'export des données:', error);
      return {};
    }
  },
};

export default {
  UserStorage,
  VoteStorage,
  AppStorage,
  StorageUtils,
  STORAGE_KEYS,
};
