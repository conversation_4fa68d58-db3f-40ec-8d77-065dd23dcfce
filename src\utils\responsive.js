import { Dimensions, PixelRatio } from 'react-native';

// Obtenir les dimensions de l'écran
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Dimensions de référence (iPhone 11)
const REFERENCE_WIDTH = 414;
const REFERENCE_HEIGHT = 896;

// Types d'écrans
export const SCREEN_TYPES = {
  SMALL: 'small',     // < 375px width
  MEDIUM: 'medium',   // 375px - 414px width
  LARGE: 'large',     // > 414px width
  TABLET: 'tablet',   // > 768px width
};

// Déterminer le type d'écran
export const getScreenType = () => {
  if (SCREEN_WIDTH < 375) return SCREEN_TYPES.SMALL;
  if (SCREEN_WIDTH <= 414) return SCREEN_TYPES.MEDIUM;
  if (SCREEN_WIDTH > 768) return SCREEN_TYPES.TABLET;
  return SCREEN_TYPES.LARGE;
};

// Fonctions de mise à l'échelle
export const scaleWidth = (size) => {
  return (SCREEN_WIDTH / REFERENCE_WIDTH) * size;
};

export const scaleHeight = (size) => {
  return (SCREEN_HEIGHT / REFERENCE_HEIGHT) * size;
};

export const scaleFont = (size) => {
  const scale = Math.min(SCREEN_WIDTH / REFERENCE_WIDTH, SCREEN_HEIGHT / REFERENCE_HEIGHT);
  const newSize = size * scale;
  return Math.max(newSize, size * 0.8); // Minimum 80% de la taille originale
};

// Fonction de mise à l'échelle modérée (recommandée pour la plupart des cas)
export const moderateScale = (size, factor = 0.5) => {
  return size + (scaleWidth(size) - size) * factor;
};

// Responsive padding et margin
export const getResponsivePadding = () => {
  const screenType = getScreenType();
  
  switch (screenType) {
    case SCREEN_TYPES.SMALL:
      return {
        small: 8,
        medium: 12,
        large: 16,
        xlarge: 20,
      };
    case SCREEN_TYPES.MEDIUM:
      return {
        small: 10,
        medium: 15,
        large: 20,
        xlarge: 25,
      };
    case SCREEN_TYPES.LARGE:
      return {
        small: 12,
        medium: 18,
        large: 24,
        xlarge: 30,
      };
    case SCREEN_TYPES.TABLET:
      return {
        small: 16,
        medium: 24,
        large: 32,
        xlarge: 40,
      };
    default:
      return {
        small: 10,
        medium: 15,
        large: 20,
        xlarge: 25,
      };
  }
};

// Tailles de police responsives
export const getResponsiveFontSizes = () => {
  const screenType = getScreenType();
  
  switch (screenType) {
    case SCREEN_TYPES.SMALL:
      return {
        tiny: 10,
        small: 12,
        medium: 14,
        large: 16,
        xlarge: 18,
        xxlarge: 20,
        huge: 24,
      };
    case SCREEN_TYPES.MEDIUM:
      return {
        tiny: 11,
        small: 13,
        medium: 15,
        large: 17,
        xlarge: 19,
        xxlarge: 22,
        huge: 26,
      };
    case SCREEN_TYPES.LARGE:
      return {
        tiny: 12,
        small: 14,
        medium: 16,
        large: 18,
        xlarge: 20,
        xxlarge: 24,
        huge: 28,
      };
    case SCREEN_TYPES.TABLET:
      return {
        tiny: 14,
        small: 16,
        medium: 18,
        large: 20,
        xlarge: 24,
        xxlarge: 28,
        huge: 32,
      };
    default:
      return {
        tiny: 11,
        small: 13,
        medium: 15,
        large: 17,
        xlarge: 19,
        xxlarge: 22,
        huge: 26,
      };
  }
};

// Dimensions responsives pour les composants
export const getResponsiveDimensions = () => {
  const screenType = getScreenType();
  const padding = getResponsivePadding();
  
  return {
    // Boutons
    buttonHeight: screenType === SCREEN_TYPES.SMALL ? 44 : 50,
    buttonRadius: screenType === SCREEN_TYPES.SMALL ? 8 : 12,
    
    // Cards
    cardRadius: screenType === SCREEN_TYPES.SMALL ? 8 : 12,
    cardPadding: padding.medium,
    
    // Icons
    iconSmall: screenType === SCREEN_TYPES.SMALL ? 16 : 20,
    iconMedium: screenType === SCREEN_TYPES.SMALL ? 20 : 24,
    iconLarge: screenType === SCREEN_TYPES.SMALL ? 24 : 30,
    
    // Avatars
    avatarSmall: screenType === SCREEN_TYPES.SMALL ? 30 : 35,
    avatarMedium: screenType === SCREEN_TYPES.SMALL ? 40 : 50,
    avatarLarge: screenType === SCREEN_TYPES.SMALL ? 60 : 80,
    
    // Spacing
    spacing: padding,
  };
};

// Vérifier si c'est un écran de tablette
export const isTablet = () => {
  return getScreenType() === SCREEN_TYPES.TABLET;
};

// Vérifier si c'est un petit écran
export const isSmallScreen = () => {
  return getScreenType() === SCREEN_TYPES.SMALL;
};

// Obtenir la largeur disponible pour le contenu
export const getContentWidth = (marginHorizontal = 20) => {
  return SCREEN_WIDTH - (marginHorizontal * 2);
};

// Calculer le nombre de colonnes pour une grille
export const getGridColumns = (itemWidth, spacing = 10) => {
  const availableWidth = getContentWidth();
  const totalItemWidth = itemWidth + spacing;
  return Math.floor(availableWidth / totalItemWidth);
};

// Styles responsives pour les layouts
export const getResponsiveStyles = () => {
  const dimensions = getResponsiveDimensions();
  const fonts = getResponsiveFontSizes();
  const screenType = getScreenType();
  
  return {
    // Container principal
    container: {
      flex: 1,
      paddingHorizontal: dimensions.spacing.medium,
    },
    
    // Headers
    headerTitle: {
      fontSize: fonts.xlarge,
      fontWeight: 'bold',
      color: '#fff',
    },
    
    headerSubtitle: {
      fontSize: fonts.medium,
      color: '#b0b0b0',
    },
    
    // Textes
    title: {
      fontSize: fonts.large,
      fontWeight: 'bold',
      color: '#fff',
    },
    
    subtitle: {
      fontSize: fonts.medium,
      color: '#b0b0b0',
    },
    
    body: {
      fontSize: fonts.medium,
      color: '#fff',
    },
    
    caption: {
      fontSize: fonts.small,
      color: '#888',
    },
    
    // Boutons
    button: {
      height: dimensions.buttonHeight,
      borderRadius: dimensions.buttonRadius,
      paddingHorizontal: dimensions.spacing.large,
      justifyContent: 'center',
      alignItems: 'center',
    },
    
    buttonText: {
      fontSize: fonts.medium,
      fontWeight: 'bold',
    },
    
    // Cards
    card: {
      backgroundColor: '#1a1a2e',
      borderRadius: dimensions.cardRadius,
      padding: dimensions.cardPadding,
      marginBottom: dimensions.spacing.medium,
    },
    
    // Inputs
    input: {
      height: dimensions.buttonHeight,
      borderRadius: dimensions.buttonRadius,
      paddingHorizontal: dimensions.spacing.medium,
      fontSize: fonts.medium,
      color: '#fff',
    },
    
    // Spacing helpers
    marginTop: {
      small: { marginTop: dimensions.spacing.small },
      medium: { marginTop: dimensions.spacing.medium },
      large: { marginTop: dimensions.spacing.large },
    },
    
    marginBottom: {
      small: { marginBottom: dimensions.spacing.small },
      medium: { marginBottom: dimensions.spacing.medium },
      large: { marginBottom: dimensions.spacing.large },
    },
    
    padding: {
      small: { padding: dimensions.spacing.small },
      medium: { padding: dimensions.spacing.medium },
      large: { padding: dimensions.spacing.large },
    },
  };
};

// Hook pour écouter les changements d'orientation
export const useOrientation = () => {
  const [orientation, setOrientation] = React.useState(
    SCREEN_WIDTH > SCREEN_HEIGHT ? 'landscape' : 'portrait'
  );
  
  React.useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setOrientation(window.width > window.height ? 'landscape' : 'portrait');
    });
    
    return () => subscription?.remove();
  }, []);
  
  return orientation;
};

export default {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  SCREEN_TYPES,
  getScreenType,
  scaleWidth,
  scaleHeight,
  scaleFont,
  moderateScale,
  getResponsivePadding,
  getResponsiveFontSizes,
  getResponsiveDimensions,
  getResponsiveStyles,
  isTablet,
  isSmallScreen,
  getContentWidth,
  getGridColumns,
  useOrientation,
};
