import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const CategoryCard = ({ 
  category, 
  hasVoted, 
  votedCandidate, 
  onPress, 
  disabled = false 
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.categoryCard, 
        hasVoted && styles.categoryCardVoted,
        disabled && styles.categoryCardDisabled
      ]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <View style={styles.categoryHeader}>
        <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
          <Ionicons name={category.icon} size={24} color="#fff" />
        </View>
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{category.name}</Text>
          <Text style={styles.categoryCount}>
            {category.candidates.length} candidats
          </Text>
        </View>
        <View style={styles.categoryStatus}>
          {hasVoted ? (
            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
          ) : (
            <Ionicons name="ellipse-outline" size={24} color="#b0b0b0" />
          )}
        </View>
      </View>

      {hasVoted && votedCandidate && (
        <View style={styles.voteInfo}>
          <Text style={styles.voteLabel}>Votre vote :</Text>
          <Text style={styles.votedCandidate}>{votedCandidate.name}</Text>
        </View>
      )}

      <View style={styles.categoryFooter}>
        <Text style={[
          styles.categoryAction,
          disabled && styles.categoryActionDisabled
        ]}>
          {disabled 
            ? 'Votes terminés' 
            : hasVoted 
              ? 'Modifier le vote' 
              : 'Voter maintenant'
          }
        </Text>
        <Ionicons 
          name="chevron-forward" 
          size={16} 
          color={disabled ? "#666" : "#b0b0b0"} 
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  categoryCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#2a2a3e',
  },
  categoryCardVoted: {
    borderColor: '#4CAF50',
  },
  categoryCardDisabled: {
    opacity: 0.6,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: 12,
  },
  categoryName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryCount: {
    color: '#b0b0b0',
    fontSize: 12,
    marginTop: 2,
  },
  categoryStatus: {
    marginLeft: 10,
  },
  voteInfo: {
    backgroundColor: '#16213e',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  voteLabel: {
    color: '#b0b0b0',
    fontSize: 12,
  },
  votedCandidate: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 2,
  },
  categoryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryAction: {
    color: '#45b7d1',
    fontSize: 14,
  },
  categoryActionDisabled: {
    color: '#666',
  },
});

export default CategoryCard;
