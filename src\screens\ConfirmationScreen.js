import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ConfirmationScreen = ({ navigation }) => {
  const [userData, setUserData] = useState(null);
  const [submissionDate, setSubmissionDate] = useState(null);
  const [countdown, setCountdown] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  // Date du tirage au sort (exemple: dans 30 jours)
  const drawDate = new Date();
  drawDate.setDate(drawDate.getDate() + 30);

  useEffect(() => {
    loadUserData();
    loadSubmissionData();
    
    // Démarrer le compte à rebours
    const timer = setInterval(updateCountdown, 1000);
    
    return () => clearInterval(timer);
  }, []);

  const loadUserData = async () => {
    try {
      const data = await AsyncStorage.getItem('userData');
      if (data) {
        setUserData(JSON.parse(data));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données utilisateur:', error);
    }
  };

  const loadSubmissionData = async () => {
    try {
      const date = await AsyncStorage.getItem('submissionDate');
      if (date) {
        setSubmissionDate(new Date(date));
      }
    } catch (error) {
      console.error('Erreur lors du chargement de la date de soumission:', error);
    }
  };

  const updateCountdown = () => {
    const now = new Date().getTime();
    const distance = drawDate.getTime() - now;

    if (distance > 0) {
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      setCountdown({ days, hours, minutes, seconds });
    } else {
      setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });
    }
  };

  const handleViewHistory = () => {
    navigation.navigate('History');
  };

  const handleBackToDashboard = () => {
    navigation.navigate('Dashboard');
  };

  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDrawDate = (date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header de confirmation */}
        <View style={styles.header}>
          <View style={styles.successIcon}>
            <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
          </View>
          <Text style={styles.title}>Félicitations !</Text>
          <Text style={styles.subtitle}>
            Vos votes ont été enregistrés avec succès
          </Text>
        </View>

        {/* Informations de participation */}
        <View style={styles.participationInfo}>
          <Text style={styles.sectionTitle}>Votre participation</Text>
          
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Ionicons name="person" size={20} color="#45b7d1" />
              <Text style={styles.infoLabel}>Participant :</Text>
              <Text style={styles.infoValue}>
                {userData ? `${userData.firstName} ${userData.lastName}` : 'Utilisateur'}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="mail" size={20} color="#45b7d1" />
              <Text style={styles.infoLabel}>Email :</Text>
              <Text style={styles.infoValue}>
                {userData ? userData.email : ''}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="time" size={20} color="#45b7d1" />
              <Text style={styles.infoLabel}>Date de soumission :</Text>
              <Text style={styles.infoValue}>
                {formatDate(submissionDate)}
              </Text>
            </View>
          </View>
        </View>

        {/* Compte à rebours */}
        <View style={styles.countdownSection}>
          <Text style={styles.sectionTitle}>Tirage au sort dans :</Text>
          
          <View style={styles.countdownContainer}>
            <View style={styles.countdownItem}>
              <Text style={styles.countdownNumber}>{countdown.days}</Text>
              <Text style={styles.countdownLabel}>Jours</Text>
            </View>
            <View style={styles.countdownItem}>
              <Text style={styles.countdownNumber}>{countdown.hours}</Text>
              <Text style={styles.countdownLabel}>Heures</Text>
            </View>
            <View style={styles.countdownItem}>
              <Text style={styles.countdownNumber}>{countdown.minutes}</Text>
              <Text style={styles.countdownLabel}>Minutes</Text>
            </View>
            <View style={styles.countdownItem}>
              <Text style={styles.countdownNumber}>{countdown.seconds}</Text>
              <Text style={styles.countdownLabel}>Secondes</Text>
            </View>
          </View>
          
          <Text style={styles.drawDate}>
            Date du tirage : {formatDrawDate(drawDate)}
          </Text>
        </View>

        {/* Informations sur les prix */}
        <View style={styles.prizesSection}>
          <Text style={styles.sectionTitle}>🎁 Prix à gagner</Text>
          <View style={styles.prizesList}>
            <View style={styles.prizeItem}>
              <Ionicons name="airplane" size={20} color="#ff6b6b" />
              <Text style={styles.prizeText}>Voyage de rêve pour 2 personnes</Text>
            </View>
            <View style={styles.prizeItem}>
              <Ionicons name="phone-portrait" size={20} color="#4ecdc4" />
              <Text style={styles.prizeText}>Smartphone dernière génération</Text>
            </View>
            <View style={styles.prizeItem}>
              <Ionicons name="card" size={20} color="#f9ca24" />
              <Text style={styles.prizeText}>Bons d'achat de 500€</Text>
            </View>
            <View style={styles.prizeItem}>
              <Ionicons name="gift" size={20} color="#9b59b6" />
              <Text style={styles.prizeText}>Et bien d'autres surprises...</Text>
            </View>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actionsSection}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleViewHistory}
            activeOpacity={0.8}
          >
            <Ionicons name="list" size={20} color="#fff" />
            <Text style={styles.primaryButtonText}>Voir mes votes</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleBackToDashboard}
            activeOpacity={0.8}
          >
            <Ionicons name="home" size={20} color="#45b7d1" />
            <Text style={styles.secondaryButtonText}>Retour au tableau de bord</Text>
          </TouchableOpacity>
        </View>

        {/* Message de rappel */}
        <View style={styles.reminderSection}>
          <Text style={styles.reminderText}>
            📱 Vous recevrez une notification avec les résultats du tirage au sort.
            Gardez l'application installée pour ne rien manquer !
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  successIcon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#b0b0b0',
    textAlign: 'center',
    lineHeight: 22,
  },
  participationInfo: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
  },
  infoCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    color: '#b0b0b0',
    fontSize: 14,
    marginLeft: 10,
    flex: 1,
  },
  infoValue: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    flex: 2,
  },
  countdownSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  countdownContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 15,
  },
  countdownItem: {
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    minWidth: 70,
  },
  countdownNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffd700',
  },
  countdownLabel: {
    fontSize: 12,
    color: '#b0b0b0',
    marginTop: 4,
  },
  drawDate: {
    color: '#b0b0b0',
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  prizesSection: {
    marginBottom: 30,
  },
  prizesList: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
  },
  prizeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  prizeText: {
    color: '#fff',
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  actionsSection: {
    marginBottom: 20,
  },
  primaryButton: {
    backgroundColor: '#45b7d1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 12,
    marginBottom: 12,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#45b7d1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 12,
  },
  secondaryButtonText: {
    color: '#45b7d1',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  reminderSection: {
    backgroundColor: '#16213e',
    borderRadius: 12,
    padding: 15,
  },
  reminderText: {
    color: '#b0b0b0',
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default ConfirmationScreen;
