// Utilitaires pour l'accessibilité dans l'application Awards

// Labels d'accessibilité pour les éléments interactifs
export const AccessibilityLabels = {
  // Navigation
  homeButton: 'Retour à l\'accueil',
  backButton: 'Retour à la page précédente',
  menuButton: 'Ouvrir le menu',
  closeButton: 'Fermer',
  
  // Authentification
  loginButton: 'Se connecter',
  registerButton: 'S\'inscrire',
  emailInput: 'Saisir votre adresse email',
  passwordInput: 'Saisir votre mot de passe',
  firstNameInput: 'Saisir votre prénom',
  lastNameInput: 'Saisir votre nom',
  phoneInput: 'Saisir votre numéro de téléphone',
  
  // Vote
  voteButton: 'Confirmer votre vote',
  candidateSelection: 'Sélectionner ce candidat',
  categoryCard: 'Ouvrir la catégorie pour voter',
  submitAllVotes: 'Envoyer tous vos votes',
  
  // Navigation entre écrans
  dashboardTab: 'Tableau de bord',
  historyTab: 'Historique des votes',
  profileTab: 'Profil utilisateur',
  
  // États
  voted: 'Vote enregistré',
  notVoted: 'Pas encore voté',
  loading: 'Chargement en cours',
  error: 'Erreur',
  success: 'Succès',
};

// Hints d'accessibilité pour expliquer les actions
export const AccessibilityHints = {
  // Vote
  candidateCard: 'Appuyez pour sélectionner ce candidat pour votre vote',
  categoryCard: 'Appuyez pour voir les candidats et voter dans cette catégorie',
  voteButton: 'Appuyez pour confirmer votre choix de candidat',
  submitVotes: 'Appuyez pour envoyer définitivement tous vos votes',
  
  // Navigation
  backButton: 'Appuyez pour revenir à l\'écran précédent',
  homeButton: 'Appuyez pour retourner à l\'accueil',
  
  // Formulaires
  textInput: 'Appuyez pour saisir du texte',
  submitForm: 'Appuyez pour valider le formulaire',
  
  // Informations
  readMore: 'Appuyez pour voir plus d\'informations',
  countdown: 'Temps restant avant le tirage au sort',
  progress: 'Progression de vos votes',
};

// Rôles d'accessibilité
export const AccessibilityRoles = {
  button: 'button',
  text: 'text',
  header: 'header',
  link: 'link',
  image: 'image',
  textInput: 'textInput',
  radioButton: 'radio',
  checkbox: 'checkbox',
  progressBar: 'progressbar',
  alert: 'alert',
  tab: 'tab',
  tabList: 'tablist',
  list: 'list',
  listItem: 'listitem',
};

// États d'accessibilité
export const AccessibilityStates = {
  selected: { selected: true },
  notSelected: { selected: false },
  checked: { checked: true },
  unchecked: { checked: false },
  disabled: { disabled: true },
  enabled: { disabled: false },
  expanded: { expanded: true },
  collapsed: { expanded: false },
};

// Fonctions utilitaires pour l'accessibilité

// Créer un label descriptif pour un candidat
export const createCandidateLabel = (candidate, category, isSelected = false) => {
  const selectionStatus = isSelected ? 'Sélectionné' : 'Non sélectionné';
  return `${candidate.name}, ${candidate.description}, catégorie ${category.name}. ${selectionStatus}`;
};

// Créer un label pour une catégorie avec statut de vote
export const createCategoryLabel = (category, hasVoted = false, votedCandidate = null) => {
  let status = hasVoted ? 'Vote enregistré' : 'Pas encore voté';
  if (hasVoted && votedCandidate) {
    status += ` pour ${votedCandidate.name}`;
  }
  return `${category.name}, ${category.candidates.length} candidats. ${status}`;
};

// Créer un label pour la progression
export const createProgressLabel = (current, total, context = 'votes') => {
  const percentage = Math.round((current / total) * 100);
  return `Progression des ${context}: ${current} sur ${total} complétés, ${percentage} pourcent`;
};

// Créer un label pour le compte à rebours
export const createCountdownLabel = (days, hours, minutes, seconds) => {
  const parts = [];
  if (days > 0) parts.push(`${days} jour${days > 1 ? 's' : ''}`);
  if (hours > 0) parts.push(`${hours} heure${hours > 1 ? 's' : ''}`);
  if (minutes > 0) parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`);
  if (seconds > 0) parts.push(`${seconds} seconde${seconds > 1 ? 's' : ''}`);
  
  return `Temps restant avant le tirage: ${parts.join(', ')}`;
};

// Vérifier si les fonctionnalités d'accessibilité sont activées
export const isAccessibilityEnabled = () => {
  // Cette fonction pourrait être étendue pour vérifier les paramètres système
  return true; // Pour l'instant, toujours activé
};

// Annoncer un message important aux lecteurs d'écran
export const announceForAccessibility = (message) => {
  // Cette fonction utiliserait l'API d'annonce de React Native
  // AccessibilityInfo.announceForAccessibility(message);
  console.log('Accessibility announcement:', message);
};

// Créer des props d'accessibilité complètes pour un bouton
export const createButtonAccessibility = (label, hint, role = 'button', state = {}) => {
  return {
    accessible: true,
    accessibilityLabel: label,
    accessibilityHint: hint,
    accessibilityRole: role,
    accessibilityState: state,
  };
};

// Créer des props d'accessibilité pour un input
export const createInputAccessibility = (label, hint, value = '') => {
  return {
    accessible: true,
    accessibilityLabel: label,
    accessibilityHint: hint,
    accessibilityRole: 'textInput',
    accessibilityValue: { text: value },
  };
};

// Créer des props d'accessibilité pour un élément de liste
export const createListItemAccessibility = (label, hint, position, total) => {
  return {
    accessible: true,
    accessibilityLabel: label,
    accessibilityHint: hint,
    accessibilityRole: 'listitem',
    accessibilityState: { 
      selected: false 
    },
    accessibilityValue: {
      text: `Élément ${position} sur ${total}`
    },
  };
};

// Créer des props d'accessibilité pour une barre de progression
export const createProgressAccessibility = (current, total, label) => {
  const percentage = Math.round((current / total) * 100);
  return {
    accessible: true,
    accessibilityLabel: label,
    accessibilityRole: 'progressbar',
    accessibilityValue: {
      min: 0,
      max: total,
      now: current,
      text: `${percentage}% complété`
    },
  };
};

// Valider les contrastes de couleurs (basique)
export const validateColorContrast = (foreground, background) => {
  // Implémentation basique - dans un vrai projet, utiliser une librairie dédiée
  // Retourne true si le contraste est suffisant
  return true; // Placeholder
};

// Tailles de police recommandées pour l'accessibilité
export const AccessibleFontSizes = {
  minimum: 12,
  small: 14,
  medium: 16,
  large: 18,
  xlarge: 20,
  xxlarge: 24,
};

// Espacement minimum recommandé pour les éléments tactiles
export const AccessibleTouchTargets = {
  minimum: 44, // Points iOS / dp Android
  recommended: 48,
  comfortable: 56,
};

// Couleurs avec bon contraste pour le thème sombre
export const AccessibleColors = {
  // Texte sur fond sombre
  primaryText: '#FFFFFF',
  secondaryText: '#B0B0B0',
  disabledText: '#666666',
  
  // Couleurs d'accent avec bon contraste
  primary: '#45B7D1',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  
  // Backgrounds
  surface: '#1A1A2E',
  background: '#0F0F23',
  card: '#16213E',
};

export default {
  AccessibilityLabels,
  AccessibilityHints,
  AccessibilityRoles,
  AccessibilityStates,
  createCandidateLabel,
  createCategoryLabel,
  createProgressLabel,
  createCountdownLabel,
  createButtonAccessibility,
  createInputAccessibility,
  createListItemAccessibility,
  createProgressAccessibility,
  isAccessibilityEnabled,
  announceForAccessibility,
  validateColorContrast,
  AccessibleFontSizes,
  AccessibleTouchTargets,
  AccessibleColors,
};
