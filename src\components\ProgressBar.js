import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';

const ProgressBar = ({ 
  progress, 
  total, 
  title = "Progression", 
  showPercentage = true,
  showFraction = true,
  color = '#4CAF50',
  backgroundColor = '#2a2a3e',
  height = 8,
  animated = true 
}) => {
  const percentage = total > 0 ? (progress / total) * 100 : 0;
  const animatedWidth = animated ? new Animated.Value(0) : null;

  React.useEffect(() => {
    if (animated && animatedWidth) {
      Animated.timing(animatedWidth, {
        toValue: percentage,
        duration: 1000,
        useNativeDriver: false,
      }).start();
    }
  }, [percentage, animated, animatedWidth]);

  const progressWidth = animated 
    ? animatedWidth?.interpolate({
        inputRange: [0, 100],
        outputRange: ['0%', '100%'],
        extrapolate: 'clamp',
      })
    : `${percentage}%`;

  return (
    <View style={styles.container}>
      {title && (
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.stats}>
            {showFraction && (
              <Text style={styles.fraction}>
                {progress}/{total}
              </Text>
            )}
            {showPercentage && (
              <Text style={styles.percentage}>
                {Math.round(percentage)}%
              </Text>
            )}
          </View>
        </View>
      )}
      
      <View style={[styles.progressBar, { height, backgroundColor }]}>
        {animated ? (
          <Animated.View 
            style={[
              styles.progressFill, 
              { 
                width: progressWidth, 
                backgroundColor: color,
                height: height - 2,
              }
            ]} 
          />
        ) : (
          <View 
            style={[
              styles.progressFill, 
              { 
                width: progressWidth, 
                backgroundColor: color,
                height: height - 2,
              }
            ]} 
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  stats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fraction: {
    color: '#b0b0b0',
    fontSize: 12,
    marginRight: 8,
  },
  percentage: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: 'bold',
  },
  progressBar: {
    borderRadius: 4,
    overflow: 'hidden',
    justifyContent: 'center',
  },
  progressFill: {
    borderRadius: 3,
    margin: 1,
  },
});

export default ProgressBar;
