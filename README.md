# Awards App 2024 🏆

Application mobile React Native pour un système de vote et tirage au sort d'awards.

## 📱 Fonctionnalités

### ✅ Implémentées

#### 🏠 Écran d'accueil
- Présentation de l'événement Awards 2024
- Aperçu des catégories disponibles
- Informations sur les prix à gagner
- Bouton d'inscription/connexion

#### 🔐 Inscription / Connexion
- Formulaire d'inscription avec validation
- Champs : nom, prénom, email, téléphone
- Validation des données en temps réel
- Simulation d'authentification
- Interface pour connexion existante
- Boutons sociaux (Google/Facebook) - UI seulement

#### 📊 Dashboard utilisateur
- Affichage des 4 catégories : Artistes, Sportifs, Influenceurs, Médias
- Barre de progression des votes
- Statut de vote par catégorie
- Bouton d'envoi final des votes (bloqué jusqu'à completion)
- Navigation vers l'historique

#### 🗳️ Système de vote
- Écran de vote par catégorie
- Sélection d'un candidat parmi plusieurs
- Sauvegarde automatique des votes
- Possibilité de modifier un vote
- Validation avant envoi final

#### ✅ Confirmation de participation
- Message de confirmation après envoi des votes
- Compte à rebours en temps réel jusqu'au tirage
- Informations sur les prix
- Enregistrement de la participation

#### 📜 Historique des votes
- Consultation des votes en lecture seule
- Statut de soumission des votes
- Détails des candidats choisis
- Progression globale

#### 💾 Stockage local
- Gestion des données utilisateur avec AsyncStorage
- Sauvegarde des votes en local
- Persistance des données entre les sessions
- Utilitaires de gestion du stockage

#### 📱 Responsive Design
- Adaptation aux différentes tailles d'écran
- Support des petits écrans, tablettes
- Mise à l'échelle intelligente des éléments
- Système de grille responsive

#### ♿ Accessibilité
- Labels d'accessibilité pour tous les éléments
- Support des lecteurs d'écran
- Contrastes de couleurs optimisés
- Tailles de police accessibles
- Zones de touch optimisées

## 🏗️ Architecture

### Structure des dossiers
```
src/
├── components/          # Composants réutilisables
│   ├── CategoryCard.js
│   ├── CandidateCard.js
│   └── ProgressBar.js
├── data/               # Données et configuration
│   └── categories.js
├── screens/            # Écrans de l'application
│   ├── HomeScreen.js
│   ├── AuthScreen.js
│   ├── DashboardScreen.js
│   ├── VotingScreen.js
│   ├── ConfirmationScreen.js
│   └── HistoryScreen.js
└── utils/              # Utilitaires
    ├── storage.js
    ├── responsive.js
    └── accessibility.js
```

### Technologies utilisées
- **React Native** 0.79.5
- **Expo** ~53.0.20
- **React Navigation** 6.x
- **AsyncStorage** pour la persistance
- **Vector Icons** pour les icônes
- **Safe Area Context** pour les zones sûres

## 🎨 Design System

### Couleurs
- **Background principal** : #0f0f23
- **Surface** : #1a1a2e
- **Accent** : #ff6b6b
- **Succès** : #4CAF50
- **Info** : #45b7d1
- **Or** : #ffd700

### Thème
- Interface sombre moderne
- Dégradés subtils
- Icônes colorées par catégorie
- Animations fluides

## 🚀 Installation et lancement

### Prérequis
- Node.js 16+
- Expo CLI
- Émulateur Android/iOS ou Expo Go

### Installation
```bash
npm install
```

### Lancement
```bash
npm start
```

## 📊 Données de démonstration

L'application utilise des données de démonstration avec :
- 4 catégories (Artistes, Sportifs, Influenceurs, Médias)
- 4 candidats par catégorie
- Informations détaillées pour chaque candidat
- Configuration des prix et de l'événement

## 🔧 Configuration

### Personnalisation
- Modifier `src/data/categories.js` pour changer les catégories/candidats
- Ajuster les couleurs dans les fichiers de style
- Configurer les dates d'événement dans `EVENT_CONFIG`

### Stockage
- Toutes les données sont stockées localement
- Clés de stockage définies dans `src/utils/storage.js`
- Fonctions utilitaires pour la gestion des données

## 📱 Fonctionnalités futures possibles

### 🔄 Non implémentées (suggestions)
- **Notifications push** réelles
- **Vérification SMS** pour l'inscription
- **Connexion sociale** fonctionnelle (Google/Facebook)
- **API backend** pour synchronisation
- **Photos des candidats**
- **Partage sur réseaux sociaux**
- **Mode hors ligne** avancé
- **Analytics** et statistiques
- **Système de commentaires**
- **Vote par géolocalisation**

## 🧪 Tests

### Tests manuels recommandés
1. **Flux complet** : Inscription → Vote → Confirmation
2. **Persistance** : Fermer/rouvrir l'app
3. **Validation** : Tester les formulaires avec données invalides
4. **Responsive** : Tester sur différentes tailles d'écran
5. **Accessibilité** : Tester avec lecteur d'écran

### Points de test
- ✅ Navigation entre écrans
- ✅ Sauvegarde des votes
- ✅ Validation des formulaires
- ✅ Compte à rebours en temps réel
- ✅ Responsive design
- ✅ Accessibilité de base

## 🐛 Problèmes connus

Aucun problème critique identifié. L'application fonctionne correctement en mode développement.

## 📄 Licence

Projet de démonstration - Usage libre pour apprentissage.

---

**Développé avec ❤️ en React Native**
