import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header avec logo */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Ionicons name="trophy" size={60} color="#ffd700" />
            <Text style={styles.logoText}>AWARDS 2024</Text>
          </View>
        </View>

        {/* Section principale */}
        <View style={styles.mainSection}>
          <Text style={styles.title}>
            Votez pour vos stars préférées !
          </Text>
          
          <Text style={styles.subtitle}>
            Participez au plus grand événement de l'année et gagnez des prix exceptionnels
          </Text>

          {/* Catégories preview */}
          <View style={styles.categoriesPreview}>
            <Text style={styles.categoriesTitle}>Catégories disponibles :</Text>
            <View style={styles.categoryList}>
              <View style={styles.categoryItem}>
                <Ionicons name="musical-notes" size={24} color="#ff6b6b" />
                <Text style={styles.categoryText}>Artistes</Text>
              </View>
              <View style={styles.categoryItem}>
                <Ionicons name="fitness" size={24} color="#4ecdc4" />
                <Text style={styles.categoryText}>Sportifs</Text>
              </View>
              <View style={styles.categoryItem}>
                <Ionicons name="people" size={24} color="#45b7d1" />
                <Text style={styles.categoryText}>Influenceurs</Text>
              </View>
              <View style={styles.categoryItem}>
                <Ionicons name="tv" size={24} color="#f9ca24" />
                <Text style={styles.categoryText}>Médias</Text>
              </View>
            </View>
          </View>

          {/* Prix à gagner */}
          <View style={styles.prizesSection}>
            <Text style={styles.prizesTitle}>🎁 Prix à gagner :</Text>
            <View style={styles.prizesList}>
              <Text style={styles.prizeItem}>• Voyage de rêve pour 2 personnes</Text>
              <Text style={styles.prizeItem}>• Smartphone dernière génération</Text>
              <Text style={styles.prizeItem}>• Bons d'achat de 500€</Text>
              <Text style={styles.prizeItem}>• Et bien d'autres surprises...</Text>
            </View>
          </View>
        </View>

        {/* Bouton d'inscription */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={styles.registerButton}
            onPress={() => navigation.navigate('Auth')}
            activeOpacity={0.8}
          >
            <Ionicons name="person-add" size={24} color="#fff" />
            <Text style={styles.registerButtonText}>S'inscrire pour voter</Text>
          </TouchableOpacity>

          <Text style={styles.infoText}>
            Inscription gratuite • Vote sécurisé • Tirage au sort équitable
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    backgroundColor: '#1a1a2e',
  },
  logoContainer: {
    alignItems: 'center',
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffd700',
    marginTop: 10,
    letterSpacing: 2,
  },
  mainSection: {
    padding: 20,
  },
  title: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 15,
  },
  subtitle: {
    fontSize: 16,
    color: '#b0b0b0',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  categoriesPreview: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    padding: 20,
    marginBottom: 25,
  },
  categoriesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
  },
  categoryList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    marginBottom: 10,
  },
  categoryText: {
    color: '#fff',
    marginLeft: 8,
    fontSize: 14,
  },
  prizesSection: {
    backgroundColor: '#16213e',
    borderRadius: 15,
    padding: 20,
    marginBottom: 30,
  },
  prizesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
  },
  prizesList: {
    paddingLeft: 10,
  },
  prizeItem: {
    color: '#b0b0b0',
    fontSize: 14,
    lineHeight: 22,
    marginBottom: 5,
  },
  actionSection: {
    padding: 20,
    alignItems: 'center',
  },
  registerButton: {
    backgroundColor: '#ff6b6b',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    width: width * 0.8,
    elevation: 5,
    shadowColor: '#ff6b6b',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  infoText: {
    color: '#b0b0b0',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 15,
    lineHeight: 18,
  },
});

export default HomeScreen;
