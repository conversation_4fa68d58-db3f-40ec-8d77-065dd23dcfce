import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Données de démonstration des catégories et candidats
const CATEGORIES_DATA = [
  {
    id: 'artists',
    name: 'Artistes',
    icon: 'musical-notes',
    color: '#ff6b6b',
    candidates: [
      { id: 'artist1', name: 'Artiste 1', description: 'Chanteur populaire' },
      { id: 'artist2', name: 'Artiste 2', description: 'Rappeur talentueux' },
      { id: 'artist3', name: 'Artiste 3', description: 'Chanteuse internationale' },
    ],
  },
  {
    id: 'sports',
    name: 'Sportifs',
    icon: 'fitness',
    color: '#4ecdc4',
    candidates: [
      { id: 'sport1', name: 'Sportif 1', description: 'Champion de football' },
      { id: 'sport2', name: 'Sportif 2', description: 'Athlète olympique' },
      { id: 'sport3', name: 'Sportif 3', description: 'Joueur de tennis' },
    ],
  },
  {
    id: 'influencers',
    name: 'Influenceurs',
    icon: 'people',
    color: '#45b7d1',
    candidates: [
      { id: 'inf1', name: 'Influenceur 1', description: 'Créateur de contenu' },
      { id: 'inf2', name: 'Influenceur 2', description: 'YouTubeur populaire' },
      { id: 'inf3', name: 'Influenceur 3', description: 'Star des réseaux sociaux' },
    ],
  },
  {
    id: 'media',
    name: 'Médias',
    icon: 'tv',
    color: '#f9ca24',
    candidates: [
      { id: 'media1', name: 'Média 1', description: 'Chaîne TV populaire' },
      { id: 'media2', name: 'Média 2', description: 'Podcast influent' },
      { id: 'media3', name: 'Média 3', description: 'Magazine en ligne' },
    ],
  },
];

const DashboardScreen = ({ navigation }) => {
  const [userData, setUserData] = useState(null);
  const [votes, setVotes] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserData();
    loadVotes();
  }, []);

  const loadUserData = async () => {
    try {
      const data = await AsyncStorage.getItem('userData');
      if (data) {
        setUserData(JSON.parse(data));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données utilisateur:', error);
    }
  };

  const loadVotes = async () => {
    try {
      const savedVotes = await AsyncStorage.getItem('userVotes');
      if (savedVotes) {
        setVotes(JSON.parse(savedVotes));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des votes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVoteForCategory = (category) => {
    navigation.navigate('Voting', { 
      category,
      currentVote: votes[category.id] 
    });
  };

  const handleSubmitAllVotes = async () => {
    const totalCategories = CATEGORIES_DATA.length;
    const votedCategories = Object.keys(votes).length;

    if (votedCategories < totalCategories) {
      Alert.alert(
        'Votes incomplets',
        `Vous devez voter dans toutes les catégories (${votedCategories}/${totalCategories} complétées).`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Confirmer l\'envoi',
      'Êtes-vous sûr de vouloir envoyer tous vos votes ? Cette action est définitive.',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Confirmer',
          onPress: async () => {
            try {
              // Marquer les votes comme envoyés
              await AsyncStorage.setItem('votesSubmitted', 'true');
              await AsyncStorage.setItem('submissionDate', new Date().toISOString());
              
              navigation.navigate('Confirmation');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible d\'envoyer les votes. Veuillez réessayer.');
            }
          },
        },
      ]
    );
  };

  const renderCategoryCard = ({ item: category }) => {
    const hasVoted = votes[category.id];
    const votedCandidate = hasVoted 
      ? category.candidates.find(c => c.id === votes[category.id])
      : null;

    return (
      <TouchableOpacity
        style={[styles.categoryCard, hasVoted && styles.categoryCardVoted]}
        onPress={() => handleVoteForCategory(category)}
        activeOpacity={0.8}
      >
        <View style={styles.categoryHeader}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
            <Ionicons name={category.icon} size={24} color="#fff" />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{category.name}</Text>
            <Text style={styles.categoryCount}>
              {category.candidates.length} candidats
            </Text>
          </View>
          <View style={styles.categoryStatus}>
            {hasVoted ? (
              <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
            ) : (
              <Ionicons name="ellipse-outline" size={24} color="#b0b0b0" />
            )}
          </View>
        </View>

        {hasVoted && votedCandidate && (
          <View style={styles.voteInfo}>
            <Text style={styles.voteLabel}>Votre vote :</Text>
            <Text style={styles.votedCandidate}>{votedCandidate.name}</Text>
          </View>
        )}

        <View style={styles.categoryFooter}>
          <Text style={styles.categoryAction}>
            {hasVoted ? 'Modifier le vote' : 'Voter maintenant'}
          </Text>
          <Ionicons name="chevron-forward" size={16} color="#b0b0b0" />
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const totalCategories = CATEGORIES_DATA.length;
  const votedCategories = Object.keys(votes).length;
  const allVotesComplete = votedCategories === totalCategories;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header utilisateur */}
        <View style={styles.userHeader}>
          <View style={styles.userInfo}>
            <Ionicons name="person-circle" size={50} color="#ffd700" />
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {userData ? `${userData.firstName} ${userData.lastName}` : 'Utilisateur'}
              </Text>
              <Text style={styles.userEmail}>
                {userData ? userData.email : ''}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.historyButton}
            onPress={() => navigation.navigate('History')}
          >
            <Ionicons name="time" size={20} color="#45b7d1" />
          </TouchableOpacity>
        </View>

        {/* Progression */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>Progression des votes</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(votedCategories / totalCategories) * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {votedCategories}/{totalCategories} catégories complétées
          </Text>
        </View>

        {/* Liste des catégories */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Catégories de vote</Text>
          <FlatList
            data={CATEGORIES_DATA}
            renderItem={renderCategoryCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </View>

        {/* Bouton d'envoi final */}
        <View style={styles.submitSection}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              !allVotesComplete && styles.submitButtonDisabled
            ]}
            onPress={handleSubmitAllVotes}
            disabled={!allVotesComplete}
            activeOpacity={0.8}
          >
            <Ionicons name="send" size={20} color="#fff" />
            <Text style={styles.submitButtonText}>
              Envoyer tous mes votes
            </Text>
          </TouchableOpacity>
          
          {!allVotesComplete && (
            <Text style={styles.submitHint}>
              Complétez tous les votes pour pouvoir les envoyer
            </Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1a1a2e',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userDetails: {
    marginLeft: 15,
    flex: 1,
  },
  userName: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  userEmail: {
    color: '#b0b0b0',
    fontSize: 14,
    marginTop: 2,
  },
  historyButton: {
    padding: 10,
  },
  progressSection: {
    padding: 20,
  },
  progressTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#2a2a3e',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  progressText: {
    color: '#b0b0b0',
    fontSize: 12,
  },
  categoriesSection: {
    padding: 20,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  categoryCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#2a2a3e',
  },
  categoryCardVoted: {
    borderColor: '#4CAF50',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: 12,
  },
  categoryName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryCount: {
    color: '#b0b0b0',
    fontSize: 12,
    marginTop: 2,
  },
  categoryStatus: {
    marginLeft: 10,
  },
  voteInfo: {
    backgroundColor: '#16213e',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  voteLabel: {
    color: '#b0b0b0',
    fontSize: 12,
  },
  votedCandidate: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 2,
  },
  categoryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryAction: {
    color: '#45b7d1',
    fontSize: 14,
  },
  submitSection: {
    padding: 20,
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    width: '100%',
  },
  submitButtonDisabled: {
    backgroundColor: '#666',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  submitHint: {
    color: '#b0b0b0',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 10,
  },
});

export default DashboardScreen;
