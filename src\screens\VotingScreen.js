import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const VotingScreen = ({ route, navigation }) => {
  const { category, currentVote } = route.params;
  const [selectedCandidate, setSelectedCandidate] = useState(currentVote || null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mettre à jour le titre de la navigation
    navigation.setOptions({
      title: `Vote - ${category.name}`,
    });
  }, [category, navigation]);

  const handleVote = async () => {
    if (!selectedCandidate) {
      Alert.alert('Erreur', 'Veuillez sélectionner un candidat');
      return;
    }

    setLoading(true);

    try {
      // Charger les votes existants
      const existingVotes = await AsyncStorage.getItem('userVotes');
      const votes = existingVotes ? JSON.parse(existingVotes) : {};

      // Ajouter/modifier le vote pour cette catégorie
      votes[category.id] = selectedCandidate;

      // Sauvegarder
      await AsyncStorage.setItem('userVotes', JSON.stringify(votes));

      const candidateName = category.candidates.find(c => c.id === selectedCandidate)?.name;

      Alert.alert(
        'Vote enregistré',
        `Votre vote pour "${candidateName}" dans la catégorie ${category.name} a été enregistré.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Impossible d\'enregistrer votre vote. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const renderCandidate = ({ item: candidate }) => {
    const isSelected = selectedCandidate === candidate.id;

    return (
      <TouchableOpacity
        style={[styles.candidateCard, isSelected && styles.candidateCardSelected]}
        onPress={() => setSelectedCandidate(candidate.id)}
        activeOpacity={0.8}
      >
        <View style={styles.candidateHeader}>
          <View style={[styles.candidateAvatar, { backgroundColor: category.color }]}>
            <Text style={styles.candidateInitial}>
              {candidate.name.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.candidateInfo}>
            <Text style={styles.candidateName}>{candidate.name}</Text>
            <Text style={styles.candidateDescription}>{candidate.description}</Text>
          </View>
          <View style={styles.selectionIndicator}>
            {isSelected ? (
              <Ionicons name="radio-button-on" size={24} color={category.color} />
            ) : (
              <Ionicons name="radio-button-off" size={24} color="#b0b0b0" />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header de la catégorie */}
        <View style={styles.categoryHeader}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
            <Ionicons name={category.icon} size={30} color="#fff" />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{category.name}</Text>
            <Text style={styles.categoryDescription}>
              Sélectionnez votre candidat préféré dans cette catégorie
            </Text>
          </View>
        </View>

        {/* Instructions */}
        <View style={styles.instructionsSection}>
          <Text style={styles.instructionsTitle}>Instructions</Text>
          <Text style={styles.instructionsText}>
            • Vous ne pouvez voter qu'une seule fois par catégorie{'\n'}
            • Votre vote peut être modifié jusqu'à l'envoi final{'\n'}
            • Tous les votes doivent être complétés avant l'envoi
          </Text>
        </View>

        {/* Liste des candidats */}
        <View style={styles.candidatesSection}>
          <Text style={styles.sectionTitle}>
            Candidats ({category.candidates.length})
          </Text>
          <FlatList
            data={category.candidates}
            renderItem={renderCandidate}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </View>

        {/* Bouton de vote */}
        <View style={styles.voteSection}>
          <TouchableOpacity
            style={[
              styles.voteButton,
              !selectedCandidate && styles.voteButtonDisabled,
              loading && styles.voteButtonLoading
            ]}
            onPress={handleVote}
            disabled={!selectedCandidate || loading}
            activeOpacity={0.8}
          >
            {loading ? (
              <Text style={styles.voteButtonText}>Enregistrement...</Text>
            ) : (
              <>
                <Ionicons name="checkmark" size={20} color="#fff" />
                <Text style={styles.voteButtonText}>
                  {currentVote ? 'Modifier mon vote' : 'Confirmer mon vote'}
                </Text>
              </>
            )}
          </TouchableOpacity>

          {selectedCandidate && (
            <View style={styles.selectedInfo}>
              <Text style={styles.selectedLabel}>Candidat sélectionné :</Text>
              <Text style={styles.selectedName}>
                {category.candidates.find(c => c.id === selectedCandidate)?.name}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  scrollView: {
    flex: 1,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1a1a2e',
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: 15,
  },
  categoryName: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  categoryDescription: {
    color: '#b0b0b0',
    fontSize: 14,
    marginTop: 4,
  },
  instructionsSection: {
    padding: 20,
    backgroundColor: '#16213e',
    margin: 20,
    borderRadius: 12,
  },
  instructionsTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  instructionsText: {
    color: '#b0b0b0',
    fontSize: 14,
    lineHeight: 20,
  },
  candidatesSection: {
    padding: 20,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  candidateCard: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  candidateCardSelected: {
    borderColor: '#4CAF50',
    backgroundColor: '#1e2a1e',
  },
  candidateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  candidateAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  candidateInitial: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  candidateInfo: {
    flex: 1,
    marginLeft: 15,
  },
  candidateName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  candidateDescription: {
    color: '#b0b0b0',
    fontSize: 14,
    marginTop: 2,
  },
  selectionIndicator: {
    marginLeft: 10,
  },
  voteSection: {
    padding: 20,
    alignItems: 'center',
  },
  voteButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    width: '100%',
  },
  voteButtonDisabled: {
    backgroundColor: '#666',
  },
  voteButtonLoading: {
    backgroundColor: '#888',
  },
  voteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  selectedInfo: {
    marginTop: 15,
    padding: 15,
    backgroundColor: '#1a1a2e',
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  selectedLabel: {
    color: '#b0b0b0',
    fontSize: 12,
  },
  selectedName: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default VotingScreen;
