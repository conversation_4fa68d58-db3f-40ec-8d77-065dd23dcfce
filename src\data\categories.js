// Données des catégories et candidats pour l'application Awards
export const CATEGORIES_DATA = [
  {
    id: 'artists',
    name: 'Artistes',
    icon: 'musical-notes',
    color: '#ff6b6b',
    description: 'Votez pour votre artiste musical préféré de l\'année',
    candidates: [
      {
        id: 'artist1',
        name: '<PERSON>',
        description: 'Chanteuse pop internationale',
        bio: 'Artiste émergente avec plus de 50 millions de streams cette année',
        achievements: ['Album #1 des ventes', 'Prix de la révélation musicale'],
        image: null, // Placeholder pour une future image
      },
      {
        id: 'artist2',
        name: '<PERSON>',
        description: 'Rappeur et producteur talentueux',
        bio: 'Pionnier du rap français nouvelle génération',
        achievements: ['Disque de platine', 'Collaboration internationale'],
        image: null,
      },
      {
        id: 'artist3',
        name: '<PERSON>',
        description: 'Chanteuse soul et R&B',
        bio: 'Voix exceptionnelle reconnue par la critique',
        achievements: ['Prix de la meilleure voix', 'Tournée mondiale'],
        image: null,
      },
      {
        id: 'artist4',
        name: 'The Electric Waves',
        description: 'Groupe de rock électronique',
        bio: 'Formation innovante mélangeant rock et électro',
        achievements: ['Festival headliner', 'Album concept acclamé'],
        image: null,
      },
    ],
  },
  {
    id: 'sports',
    name: 'Sportifs',
    icon: 'fitness',
    color: '#4ecdc4',
    description: 'Célébrez les performances sportives exceptionnelles',
    candidates: [
      {
        id: 'sport1',
        name: 'Marcus Champion',
        description: 'Champion de football professionnel',
        bio: 'Meilleur buteur de la saison avec 35 buts',
        achievements: ['Ballon d\'or national', 'Capitaine de l\'équipe nationale'],
        image: null,
      },
      {
        id: 'sport2',
        name: 'Elena Swift',
        description: 'Athlète olympique - Sprint',
        bio: 'Recordwoman du 100m et 200m',
        achievements: ['Médaille d\'or olympique', 'Record du monde junior'],
        image: null,
      },
      {
        id: 'sport3',
        name: 'Rafael Court',
        description: 'Joueur de tennis professionnel',
        bio: 'Vainqueur de 3 tournois du Grand Chelem cette année',
        achievements: ['#1 mondial', 'Masters Cup winner'],
        image: null,
      },
      {
        id: 'sport4',
        name: 'Team Phoenix',
        description: 'Équipe de basketball',
        bio: 'Champions nationaux invaincus cette saison',
        achievements: ['Championnat national', 'Coupe européenne'],
        image: null,
      },
    ],
  },
  {
    id: 'influencers',
    name: 'Influenceurs',
    icon: 'people',
    color: '#45b7d1',
    description: 'Reconnaissez l\'impact des créateurs de contenu',
    candidates: [
      {
        id: 'inf1',
        name: 'TechGuru_Max',
        description: 'Créateur de contenu technologique',
        bio: 'Expert en nouvelles technologies avec 2M d\'abonnés',
        achievements: ['Chaîne tech #1', 'Partenariats exclusifs'],
        image: null,
      },
      {
        id: 'inf2',
        name: 'CookingWithAmy',
        description: 'YouTubeuse culinaire populaire',
        bio: 'Recettes créatives et accessibles pour tous',
        achievements: ['Livre de cuisine bestseller', '5M d\'abonnés'],
        image: null,
      },
      {
        id: 'inf3',
        name: 'FitnessJoe',
        description: 'Coach sportif et influenceur fitness',
        bio: 'Motivation et conseils pour une vie saine',
        achievements: ['Application fitness #1', 'Programme certifié'],
        image: null,
      },
      {
        id: 'inf4',
        name: 'ArtisticSoul',
        description: 'Créatrice de contenu artistique',
        bio: 'Tutoriels d\'art et inspiration créative',
        achievements: ['Exposition virtuelle', 'Collaboration musées'],
        image: null,
      },
    ],
  },
  {
    id: 'media',
    name: 'Médias',
    icon: 'tv',
    color: '#f9ca24',
    description: 'Votez pour vos médias et contenus préférés',
    candidates: [
      {
        id: 'media1',
        name: 'StreamVision',
        description: 'Plateforme de streaming innovante',
        bio: 'Contenu original et exclusivités mondiales',
        achievements: ['Série la plus regardée', 'Innovation technologique'],
        image: null,
      },
      {
        id: 'media2',
        name: 'PodcastPrime',
        description: 'Réseau de podcasts influent',
        bio: 'Discussions approfondies sur l\'actualité et la culture',
        achievements: ['Podcast #1 en France', 'Prix du journalisme audio'],
        image: null,
      },
      {
        id: 'media3',
        name: 'DigitalNews24',
        description: 'Magazine en ligne d\'actualités',
        bio: 'Information rapide et vérifiée 24h/24',
        achievements: ['Site d\'info le plus consulté', 'Prix de l\'innovation'],
        image: null,
      },
      {
        id: 'media4',
        name: 'CultureScope',
        description: 'Média culturel et artistique',
        bio: 'Découverte et promotion de la culture émergente',
        achievements: ['Meilleur média culturel', 'Partenariats artistiques'],
        image: null,
      },
    ],
  },
];

// Utilitaires pour manipuler les données
export const CategoriesUtils = {
  // Obtenir une catégorie par son ID
  getCategoryById(categoryId) {
    return CATEGORIES_DATA.find(category => category.id === categoryId);
  },

  // Obtenir un candidat par son ID et l'ID de sa catégorie
  getCandidateById(categoryId, candidateId) {
    const category = this.getCategoryById(categoryId);
    return category?.candidates.find(candidate => candidate.id === candidateId);
  },

  // Obtenir tous les candidats d'une catégorie
  getCandidatesByCategory(categoryId) {
    const category = this.getCategoryById(categoryId);
    return category?.candidates || [];
  },

  // Rechercher des candidats par nom
  searchCandidates(searchTerm) {
    const results = [];
    CATEGORIES_DATA.forEach(category => {
      category.candidates.forEach(candidate => {
        if (candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            candidate.description.toLowerCase().includes(searchTerm.toLowerCase())) {
          results.push({
            ...candidate,
            categoryId: category.id,
            categoryName: category.name,
            categoryColor: category.color,
          });
        }
      });
    });
    return results;
  },

  // Obtenir le nombre total de candidats
  getTotalCandidatesCount() {
    return CATEGORIES_DATA.reduce((total, category) => {
      return total + category.candidates.length;
    }, 0);
  },

  // Obtenir le nombre de candidats par catégorie
  getCandidatesCountByCategory() {
    const counts = {};
    CATEGORIES_DATA.forEach(category => {
      counts[category.id] = category.candidates.length;
    });
    return counts;
  },

  // Valider qu'un vote est valide
  isValidVote(categoryId, candidateId) {
    const candidate = this.getCandidateById(categoryId, candidateId);
    return !!candidate;
  },

  // Obtenir des statistiques sur les catégories
  getCategoriesStats() {
    return {
      totalCategories: CATEGORIES_DATA.length,
      totalCandidates: this.getTotalCandidatesCount(),
      averageCandidatesPerCategory: Math.round(this.getTotalCandidatesCount() / CATEGORIES_DATA.length),
      categoriesWithMostCandidates: CATEGORIES_DATA
        .sort((a, b) => b.candidates.length - a.candidates.length)
        .slice(0, 3)
        .map(cat => ({ name: cat.name, count: cat.candidates.length })),
    };
  },
};

// Configuration des prix et récompenses
export const PRIZES_CONFIG = {
  grandPrize: {
    name: 'Voyage de rêve pour 2 personnes',
    description: 'Séjour d\'une semaine dans une destination de luxe',
    value: '5000€',
    icon: 'airplane',
    color: '#ff6b6b',
  },
  secondPrize: {
    name: 'Smartphone dernière génération',
    description: 'Le dernier modèle avec tous les accessoires',
    value: '1200€',
    icon: 'phone-portrait',
    color: '#4ecdc4',
  },
  thirdPrize: {
    name: 'Bons d\'achat',
    description: 'Utilisables dans vos magasins préférés',
    value: '500€',
    icon: 'card',
    color: '#f9ca24',
  },
  consolationPrizes: [
    {
      name: 'Casque audio premium',
      description: 'Son haute qualité pour vos musiques préférées',
      value: '200€',
      icon: 'headset',
      color: '#9b59b6',
    },
    {
      name: 'Abonnement streaming',
      description: '1 an d\'accès à toutes vos séries et films',
      value: '120€',
      icon: 'tv',
      color: '#e74c3c',
    },
    {
      name: 'Box gourmande',
      description: 'Sélection de produits gastronomiques',
      value: '80€',
      icon: 'restaurant',
      color: '#f39c12',
    },
  ],
};

// Configuration de l'événement
export const EVENT_CONFIG = {
  name: 'Awards 2024',
  description: 'Le plus grand événement de vote de l\'année',
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31'),
  drawDate: new Date('2025-01-15'),
  maxVotesPerUser: 1, // Un vote par catégorie
  minVotesRequired: CATEGORIES_DATA.length, // Toutes les catégories
  organizerName: 'Awards Organization',
  contactEmail: '<EMAIL>',
  website: 'https://awards2024.com',
  socialMedia: {
    facebook: '@awards2024',
    twitter: '@awards2024',
    instagram: '@awards2024',
  },
};

export default {
  CATEGORIES_DATA,
  CategoriesUtils,
  PRIZES_CONFIG,
  EVENT_CONFIG,
};
